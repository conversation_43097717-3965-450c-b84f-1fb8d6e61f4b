  videoCount: 56
}
Auth state: { authState: false, userId: undefined }
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
 GET /style.css?t=1710289820 404 in 324ms
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 451ms
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 447ms
Auth state: { authState: false, userId: undefined }
 GET /base64.js?t=1688387834 404 in 98ms
API: Fetching related content for movie with ID 574475
Auth state: { authState: false, userId: undefined }
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 76ms
 GET /sources.js?t=1745104089 404 in 82ms
Auth state: { authState: false, userId: undefined }
 GET /reporting.js?t=1688387834 404 in 64ms
Auth state: { authState: false, userId: undefined }
 GET /sbx.js?t=1688387834 404 in 110ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 52ms
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
 GET /watch/574475?forcePlay=true&contentType=movie 200 in 100ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 82ms
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 399ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 574475
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 136ms
Auth state: { authState: false, userId: undefined }
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 471ms
 GET /style.css?t=1710289820 404 in 316ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /base64.js?t=1688387834 404 in 125ms
 GET /sources.js?t=1745104089 404 in 124ms
API: Fetching related content for movie with ID 574475
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 65ms
Auth state: { authState: false, userId: undefined }
 GET /reporting.js?t=1688387834 404 in 64ms
Auth state: { authState: false, userId: undefined }
 GET /sbx.js?t=1688387834 404 in 54ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 62ms
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
 GET /watch/574475?forcePlay=true&contentType=movie 200 in 93ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 69ms
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 299ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 574475
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Auth state: { authState: false, userId: undefined }
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
 GET /style.css?t=1710289820 404 in 256ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 493ms
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 497ms
 GET /base64.js?t=1688387834 404 in 131ms
 GET /sources.js?t=1745104089 404 in 131ms
API: Fetching related content for movie with ID 574475
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 61ms
Auth state: { authState: false, userId: undefined }
 GET /reporting.js?t=1688387834 404 in 57ms
Auth state: { authState: false, userId: undefined }
 GET /sbx.js?t=1688387834 404 in 40ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 74ms
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
 GET /watch/574475?forcePlay=true&contentType=movie 200 in 101ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 84ms
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 302ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 574475
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Auth state: { authState: false, userId: undefined }
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
 GET /style.css?t=1710289820 404 in 292ms
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 392ms
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 411ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /base64.js?t=1688387834 404 in 193ms
 GET /sources.js?t=1745104089 404 in 189ms
Auth state: { authState: false, userId: undefined }
 GET /reporting.js?t=1688387834 404 in 69ms
API: Fetching related content for movie with ID 574475
Auth state: { authState: false, userId: undefined }
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 69ms
 GET /sbx.js?t=1688387834 404 in 70ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 54ms
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
 GET /watch/574475?forcePlay=true&contentType=movie 200 in 64ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 46ms
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 408ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 574475
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Auth state: { authState: false, userId: undefined }
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
 GET /style.css?t=1710289820 404 in 79ms
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 192ms
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 228ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /base64.js?t=1688387834 404 in 102ms
 GET /sources.js?t=1745104089 404 in 89ms
Auth state: { authState: false, userId: undefined }
 GET /reporting.js?t=1688387834 404 in 71ms
API: Fetching related content for movie with ID 574475
Auth state: { authState: false, userId: undefined }
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 90ms
 GET /sbx.js?t=1688387834 404 in 95ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 41ms
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
 GET /watch/574475?forcePlay=true&contentType=movie 200 in 72ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 41ms
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 359ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 574475
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Auth state: { authState: false, userId: undefined }
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
 GET /style.css?t=1710289820 404 in 86ms
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 187ms
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 217ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /base64.js?t=1688387834 404 in 125ms
 GET /sources.js?t=1745104089 404 in 124ms
Auth state: { authState: false, userId: undefined }
 GET /reporting.js?t=1688387834 404 in 84ms
API: Fetching related content for movie with ID 574475
Auth state: { authState: false, userId: undefined }
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 103ms
 GET /sbx.js?t=1688387834 404 in 103ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 60ms
Auth state: { authState: false, userId: undefined }
 GET /watch/574475?forcePlay=true&contentType=movie 200 in 87ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424615844; HstPn4873540=14; HstPt4873540=1081',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/watch/574475?forcePlay=true&contentType=movie',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Finding user by ID for session validation: 67f2bb470ba03bb2e0912c3f
 POST /api/watch-party 200 in 148ms
User found successfully
Session validation successful for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 197ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424615844; HstPn4873540=14; HstPt4873540=1081',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/watch/574475?forcePlay=true&contentType=movie',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Using cached session validation for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 153ms
 GET /api/profiles?userId=67f2bb470ba03bb2e0912c3f 200 in 240ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 291ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 GET /api/profiles/67f2bb470ba03bb2e0912c41/my-list 200 in 121ms
 POST /api/watch-party 200 in 54ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 243ms
 GET /api/notifications?userId=67f2bb470ba03bb2e0912c3f 200 in 319ms
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 363ms
Fetching from TMDB: /3/movie/574475?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 574475
TMDB API success for /movie/574475 { resultCount: 'N/A' }
Fetched movie details for ID 574475: {
  title: 'Final Destination Bloodlines',
  hasVideos: true,
  videoCount: 56
}
Enhancing movie content with OMDB data for IMDb ID: tt9619824
Fetching from OMDB: http://www.omdbapi.com/?i=tt9619824&plot=full
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 140ms
OMDB API success for {"i":"tt9619824","plot":"full"}
API: Fetched movie content: {
  id: '574475',
  title: 'Final Destination Bloodlines',
  imdbId: 'tt9619824',
  tmdbId: '574475',
  dataSource: 'both'
}
 GET /api/content?id=574475&type=movie 200 in 196ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /style.css?t=1710289820 404 in 288ms
 GET /sbx.js?t=1688387834 404 in 287ms
 GET /base64.js?t=1688387834 404 in 286ms
 GET /sources.js?t=1745104089 404 in 299ms
 GET /reporting.js?t=1688387834 404 in 297ms
API: Fetching related content for movie with ID 574475
API: Found 20 related content items
 GET /api/related-content?id=574475&type=movie 200 in 60ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 47ms
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
Auth state: { authState: false, userId: undefined }
 GET /movies 200 in 2801ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424662960; HstPn4873540=15; HstPt4873540=1082',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/movies',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Finding user by ID for session validation: 67f2bb470ba03bb2e0912c3f
User found successfully
Session validation successful for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 147ms
 POST /api/watch-party 200 in 165ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424662960; HstPn4873540=15; HstPt4873540=1082',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/movies',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Using cached session validation for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 171ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 298ms
 GET /api/profiles?userId=67f2bb470ba03bb2e0912c3f 200 in 199ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 321ms
 GET /api/profiles/67f2bb470ba03bb2e0912c41/my-list 200 in 109ms
 GET /api/notifications?userId=67f2bb470ba03bb2e0912c3f 200 in 424ms
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
 ⚠ ./src/app/api/watch-party/route.ts
Module not found: Can't resolve './sse/route' in 'C:\Users\<USER>\Desktop\StreamVista\src\app\api\watch-party'

Import trace for requested module:
./src/app/api/watch-party/route.ts
[formatTMDbContentForCards] Processing 4 items
[formatTMDbContentForCards] After filtering: 4 valid items
 GET /movies 200 in 59ms
 GET / 200 in 1914ms
 GET /movies 200 in 28ms
Auth state: { authState: false, userId: undefined }
 GET /movies 200 in 328ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424662960; HstPn4873540=15; HstPt4873540=1082',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/movies',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Finding user by ID for session validation: 67f2bb470ba03bb2e0912c3f
 POST /api/watch-party 200 in 136ms
User found successfully
Session validation successful for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 168ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424662960; HstPn4873540=15; HstPt4873540=1082',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/movies',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Using cached session validation for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 142ms
 GET /api/profiles?userId=67f2bb470ba03bb2e0912c3f 200 in 230ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 243ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 175ms
 GET /api/notifications?userId=67f2bb470ba03bb2e0912c3f 200 in 416ms
 GET /api/profiles/67f2bb470ba03bb2e0912c41/my-list 200 in 96ms
 GET /movies 200 in 29ms
Auth state: { authState: false, userId: undefined }
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 120ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424662960; HstPn4873540=15; HstPt4873540=1082',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/watch/1233413?forcePlay=true&contentType=movie',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Using cached session validation for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 33ms
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752424662960; HstPn4873540=15; HstPt4873540=1082',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/watch/1233413?forcePlay=true&contentType=movie',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Using cached session validation for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 84ms
 POST /api/watch-party 200 in 95ms
 GET /api/profiles?userId=67f2bb470ba03bb2e0912c3f 200 in 121ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 239ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 243ms
 GET /api/profiles/67f2bb470ba03bb2e0912c41/my-list 200 in 97ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 70ms
 GET /api/notifications?userId=67f2bb470ba03bb2e0912c3f 200 in 321ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 614ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Proxying request to: https://vidsrc.xyz/embed/movie?imdb=tt31193180
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 249ms
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 489ms
API: Fetching related content for movie with ID 1233413
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 77ms
 GET /api/proxy/vidsrc?url=https%3A%2F%2Fvidsrc.xyz%2Fembed%2Fmovie%3Fimdb%3Dtt31193180 200 in 939ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /style.css?t=1710289820 404 in 232ms
 GET /base64.js?t=1688387834 404 in 224ms
 GET /sbx.js?t=1688387834 404 in 216ms
 GET /sources.js?t=1745104089 404 in 221ms
 GET /reporting.js?t=1688387834 404 in 220ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752423799 404 in 53ms